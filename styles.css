/* Reset và Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON>o', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Particle Background */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(211, 47, 47, 0.3);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.particle:nth-child(odd) {
    background: rgba(255, 215, 0, 0.3);
    animation-delay: -2s;
}

.particle:nth-child(3n) {
    animation-delay: -4s;
    animation-duration: 8s;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }

    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.8;
    }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translateY(0);
}

.header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.15);
}

.header.scrolling {
    transition: all 0.2s ease;
}

.navbar {
    padding: 1rem 0;
    transition: padding 0.3s ease;
}

.header.scrolled .navbar {
    padding: 0.75rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #d32f2f;
    transition: all 0.3s ease;
    text-decoration: none;
}

.nav-logo:hover {
    color: #b71c1c;
    transform: scale(1.05);
}

.nav-logo i {
    margin-right: 10px;
    color: #ffd700;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.nav-logo:hover i {
    transform: rotate(360deg) scale(1.2);
    color: #ffb300;
}

.header.scrolled .nav-logo {
    font-size: 1.3rem;
}

.header.scrolled .nav-logo i {
    font-size: 1.1rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: #d32f2f;
    background: rgba(211, 47, 47, 0.1);
    transform: translateY(-2px);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.hamburger:hover {
    background: rgba(211, 47, 47, 0.1);
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 2px;
    transform-origin: center;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
    background: #d32f2f;
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
    transform: scale(0);
}

.hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
    background: #d32f2f;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #d32f2f 0%, #f44336 50%, #ff5722 100%);
    color: white;
    padding-top: 80px;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="star" patternUnits="userSpaceOnUse" width="20" height="20"><polygon points="10,1 13,7 19,7 14.5,11 16.5,17 10,13.5 3.5,17 5.5,11 1,7 7,7" fill="rgba(255,215,0,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23star)"/></svg>') repeat;
    animation: moveStars 20s linear infinite;
}

@keyframes moveStars {
    0% {
        transform: translateX(0) translateY(0);
    }

    100% {
        transform: translateX(-20px) translateY(-20px);
    }
}

.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(255, 215, 0, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.hero-content {
    flex: 1;
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

@keyframes shimmer {

    0%,
    100% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    opacity: 0.9;
    animation: fadeInUp 1s ease 0.2s both;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-date {
    font-size: 2rem;
    font-weight: 300;
    margin-bottom: 2rem;
    color: #ffd700;
    animation: fadeInUp 1s ease 0.4s both;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

@keyframes glow {
    from {
        text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
    }

    to {
        text-shadow: 0 0 30px rgba(255, 215, 0, 0.8), 0 0 40px rgba(255, 215, 0, 0.6);
    }
}

.cta-button {
    display: inline-block;
    padding: 15px 30px;
    background: linear-gradient(45deg, #ffd700, #ffeb3b, #ffd700);
    background-size: 200% 200%;
    color: #333;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    animation: fadeInUp 1s ease 0.6s both;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 40px rgba(255, 215, 0, 0.4);
    background-position: 100% 100%;
}

.hero-image {
    flex: 1;
    text-align: center;
    padding: 0 20px;
}

/* Video Container Styles */
.video-container {
    position: relative;
    display: inline-block;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 4px;
    animation: fadeInRight 1s ease 0.8s both;
    transition: all 0.3s ease;
    max-width: 100%;
    margin: 0 auto;
    padding: 3px;
    border-radius: 15px;
    cursor: pointer;
}

.video-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

.video-wrapper {
    position: relative;
    width: 100%;
    max-width: 560px;
    height: 315px;
    border-radius: 16px;
    overflow: hidden;
    background: #000;
    aspect-ratio: 16/9;
    display: none;
    /* Hidden by default */
}

.video-wrapper.active {
    display: block;
}

.video-wrapper iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 16px;
    display: block;
}

.video-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 20px;
    transform: translateY(100%);
    transition: all 0.3s ease;
    border-radius: 0 0 16px 16px;
}

.video-container:hover .video-overlay {
    transform: translateY(0);
}

.video-info {
    text-align: left;
}

.video-info h3 {
    margin: 0 0 8px 0;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.video-info h3 i {
    color: #dc3545;
    font-size: 1.2rem;
}

.video-info p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
    color: #f8f9fa;
}

.video-info-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 20px;
    border-radius: 0 0 16px 16px;
}

.video-info-overlay h3 {
    margin: 0 0 8px 0;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.video-info-overlay h3 i {
    color: #dc3545;
    font-size: 1.2rem;
}

.video-info-overlay p {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    opacity: 0.9;
    color: #f8f9fa;
}

.video-duration {
    background: rgba(0, 0, 0, 0.8);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    position: absolute;
    top: 15px;
    right: 15px;
    color: white;
}

/* Video Thumbnail Styles */
.video-thumbnail {
    position: relative;
    width: 100%;
    max-width: 560px;
    height: 315px;
    border-radius: 16px;
    overflow: hidden;
    background: #000;
    cursor: pointer;
    transition: all 0.3s ease;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.video-thumbnail:hover img {
    transform: scale(1.05);
    filter: brightness(0.8);
}

/* Play Button Overlay */
.play-button-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.play-button {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.play-button i {
    font-size: 24px;
    color: #dc3545;
    margin-left: 4px;
    transition: all 0.3s ease;
}

.video-thumbnail:hover .play-button {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

.video-thumbnail:hover .play-button i {
    color: #b71c1c;
    transform: scale(1.2);
}

/* Hero Video Responsive */
.hero-image {
    padding: 0 10px;
    margin-top: 2rem;
}

.video-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 3px;
    border-radius: 15px;
}

.video-wrapper,
.video-thumbnail {
    max-width: 100%;
    height: auto;
    min-height: 200px;
    border-radius: 12px;
}

.video-wrapper iframe {
    border-radius: 12px;
    min-height: 200px;
}

.video-info-overlay {
    padding: 15px;
}

.video-info-overlay h3 {
    font-size: 1rem;
}

.video-info-overlay p {
    font-size: 0.85rem;
}

.play-button {
    width: 60px;
    height: 60px;
}

.play-button i {
    font-size: 18px;
}

.video-duration {
    top: 10px;
    right: 10px;
    font-size: 0.75rem;
    padding: 3px 6px;
}

/* Tablet Video Responsive */
@media (max-width: 1024px) and (min-width: 769px) {
    .hero-image {
        padding: 0 15px;
    }

    .video-container {
        max-width: 90%;
    }

    .video-wrapper {
        max-width: 480px;
        height: 270px;
    }
}

/* Small Mobile Video */
@media (max-width: 480px) {
    .hero-image {
        padding: 0 5px;
    }

    .video-container {
        padding: 2px;
        border-radius: 12px;
    }

    .video-wrapper {
        min-height: 180px;
        border-radius: 10px;
    }

    .video-wrapper iframe {
        border-radius: 10px;
        min-height: 180px;
    }

    .video-overlay {
        padding: 12px;
    }

    .video-info h3 {
        font-size: 0.9rem;
    }

    .video-info h3 i {
        font-size: 1rem;
    }

    .video-info p {
        font-size: 0.8rem;
    }
}

/* Video Aspect Ratio for Modern Browsers */
@supports (aspect-ratio: 16 / 9) {
    .video-wrapper {
        height: auto;
        aspect-ratio: 16 / 9;
    }

    @media (max-width: 768px) {
        .video-wrapper {
            aspect-ratio: 16 / 9;
            min-height: unset;
        }

        .video-wrapper iframe {
            min-height: unset;
        }
    }

    @media (max-width: 480px) {
        .video-wrapper iframe {
            min-height: unset;
        }
    }
}

/* Timeline Navigation */
.timeline-nav {
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    overflow: hidden;
}

.timeline-nav::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><circle cx="30" cy="30" r="2" fill="rgba(211,47,47,0.1)"/></svg>') repeat;
    animation: movePattern 30s linear infinite;
}

@keyframes movePattern {
    0% {
        transform: translateX(0) translateY(0);
    }

    100% {
        transform: translateX(60px) translateY(60px);
    }
}

.timeline-nav h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
    position: relative;
    z-index: 2;
    background: linear-gradient(45deg, #d32f2f, #f44336, #d32f2f);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: shimmer 4s ease-in-out infinite;
}

.timeline-items {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow-x: auto;
    padding: 2rem 0;
}

.timeline-items::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #d32f2f, #ffd700, #d32f2f);
    background-size: 200% 200%;
    animation: shimmer 3s ease-in-out infinite;
    z-index: 1;
    border-radius: 2px;
    box-shadow: 0 2px 10px rgba(211, 47, 47, 0.3);
}

.timeline-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    cursor: pointer;
    transition: all 0.3s ease;
}

.timeline-item:hover {
    transform: scale(1.15) translateY(-5px);
}

.timeline-item.active .year {
    background: linear-gradient(135deg, #d32f2f, #f44336);
    color: white;
    box-shadow: 0 10px 30px rgba(211, 47, 47, 0.4);
    animation: pulse-active 2s ease-in-out infinite;
}

@keyframes pulse-active {

    0%,
    100% {
        transform: scale(1);
        box-shadow: 0 10px 30px rgba(211, 47, 47, 0.4);
    }

    50% {
        transform: scale(1.05);
        box-shadow: 0 15px 40px rgba(211, 47, 47, 0.6);
    }
}

.year {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: white;
    border: 3px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.event {
    text-align: center;
    font-size: 0.9rem;
    font-weight: 500;
    max-width: 120px;
}

/* Sections */
.section {
    padding: 5rem 0;
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #333;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: #d32f2f;
}

.bg-light {
    background: #f8f9fa;
}

.bg-primary {
    background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
    color: white;
}

.text-white {
    color: white;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.content-text h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: #d32f2f;
}

.content-text p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.8;
}

.content-text ul {
    list-style: none;
    padding-left: 0;
}

.content-text li {
    padding: 0.5rem 0;
    padding-left: 2rem;
    position: relative;
}

.content-text li::before {
    content: '★';
    position: absolute;
    left: 0;
    color: #ffd700;
    font-weight: bold;
}

.content-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* Image Placeholders */
.image-placeholder {
    width: 100%;
    height: 250px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 2rem;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid rgba(211, 47, 47, 0.1);
}

.image-placeholder:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(211, 47, 47, 0.15);
    border-color: rgba(211, 47, 47, 0.3);
}

.image-placeholder i {
    font-size: 3rem;
    color: #d32f2f;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.image-placeholder:hover i {
    transform: scale(1.1);
}

.image-placeholder h4 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.image-placeholder p {
    color: #666;
    font-size: 0.9rem;
}

/* Video Placeholders */
.video-placeholder {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.video-placeholder i {
    font-size: 2rem;
    color: rgba(255, 255, 255, 0.7);
    position: absolute;
    top: 20px;
    left: 20px;
}

.video-placeholder .play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(211, 47, 47, 0.1);
}

.uprising-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(211, 47, 47, 0.1), transparent);
    transition: left 0.6s ease;
}

.uprising-card:hover::before {
    left: 100%;
}

.uprising-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 25px 50px rgba(211, 47, 47, 0.2);
    border-color: rgba(211, 47, 47, 0.3);
}

.uprising-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #d32f2f;
}

.uprising-card .date {
    font-weight: 700;
    color: #ffd700;
    background: linear-gradient(135deg, #d32f2f, #f44336);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    display: inline-block;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(211, 47, 47, 0.3);
    animation: float-date 3s ease-in-out infinite;
    position: relative;
    overflow: hidden;
}

.uprising-card .date::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.uprising-card:hover .date::before {
    left: 100%;
}

@keyframes float-date {

    0%,
    100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-3px);
    }
}

/* Revolution Timeline */
.revolution-timeline {
    display: grid;
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.rev-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.rev-item.highlight {
    background: rgba(255, 215, 0, 0.2);
    border: 2px solid #ffd700;
}

.rev-date {
    min-width: 120px;
    font-weight: 700;
    color: #ffd700;
}

.rev-event {
    flex: 1;
    font-size: 1.1rem;
}

/* Declaration */
.declaration-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

blockquote {
    font-size: 1.5rem;
    font-style: italic;
    padding: 2rem;
    background: #f8f9fa;
    border-left: 5px solid #d32f2f;
    margin-bottom: 2rem;
}

cite {
    display: block;
    text-align: right;
    margin-top: 1rem;
    font-weight: 700;
    color: #d32f2f;
}

/* Significance Grid */
.significance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.significance-item {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.significance-item:hover {
    transform: translateY(-10px);
}

.significance-item i {
    font-size: 3rem;
    color: #d32f2f;
    margin-bottom: 1rem;
}

.significance-item h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

/* Debate Section */
.debate-content {
    max-width: 800px;
    margin: 0 auto;
}

.wrong-view {
    background: #ffebee;
    padding: 2rem;
    border-radius: 10px;
    border-left: 5px solid #f44336;
    margin-bottom: 3rem;
}

.wrong-view h3 {
    color: #d32f2f;
    margin-bottom: 1rem;
}

.counter-arguments h3 {
    color: #2e7d32;
    margin-bottom: 2rem;
}

.argument-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.argument {
    background: #e8f5e8;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 5px solid #4caf50;
}

.argument h4 {
    color: #2e7d32;
    margin-bottom: 0.5rem;
}

/* Gallery */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    background: white;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
}

.gallery-item:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.gallery-item .image-placeholder {
    position: relative;
    height: 280px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px 20px 0 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0;
    border: none;
    box-shadow: none;
}

.gallery-item .image-placeholder img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s ease;
    border-radius: 0;
}

.gallery-item:hover .image-placeholder img {
    transform: scale(1.1);
    filter: brightness(0.9);
}

.gallery-item .image-placeholder h4,
.gallery-item .image-placeholder p {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    margin: 0;
    padding: 1.5rem;
    transform: translateY(100%);
    transition: all 0.4s ease;
    text-align: left;
}

.gallery-item .image-placeholder h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.gallery-item .image-placeholder p {
    font-size: 0.9rem;
    opacity: 0.9;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.5);
    transform: translateY(calc(100% - 3rem));
}

.gallery-item:hover .image-placeholder h4,
.gallery-item:hover .image-placeholder p {
    transform: translateY(0);
}

/* Gallery item badge */
.gallery-item::before {
    content: '';
    position: absolute;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    background: rgba(211, 47, 47, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.gallery-item::after {
    content: '🖼️';
    position: absolute;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    z-index: 3;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.gallery-item:hover::before,
.gallery-item:hover::after {
    opacity: 1;
    transform: scale(1);
}

/* Gallery loading animation */
.gallery-item {
    animation: fadeInUp 0.6s ease forwards;
}

.gallery-item:nth-child(1) {
    animation-delay: 0.1s;
}

.gallery-item:nth-child(2) {
    animation-delay: 0.2s;
}

.gallery-item:nth-child(3) {
    animation-delay: 0.3s;
}

.gallery-item:nth-child(4) {
    animation-delay: 0.4s;
}

.gallery-item:nth-child(5) {
    animation-delay: 0.5s;
}

.gallery-item:nth-child(6) {
    animation-delay: 0.6s;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Video Section */
.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.video-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.video-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.video-thumbnail {
    position: relative;
    width: 100%;
    max-width: 560px;
    height: 315px;
    border-radius: 16px;
    overflow: hidden;
    background: #000;
    cursor: pointer;
    transition: all 0.3s ease;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.video-thumbnail:hover img {
    transform: scale(1.05);
    filter: brightness(0.8);
}

/* Play Button Overlay */
.play-button-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.play-button {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.play-button i {
    font-size: 24px;
    color: #dc3545;
    margin-left: 4px;
    /* Align play icon slightly to the right */
    transition: all 0.3s ease;
}

.video-thumbnail:hover .play-button {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

.video-thumbnail:hover .play-button i {
    color: #b71c1c;
    transform: scale(1.2);
}

/* Documents Section */
.documents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.document-category {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.document-category h3 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #d32f2f;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.document-category h3 i {
    font-size: 1.3rem;
}

.document-list {
    list-style: none;
    padding: 0;
}

.document-list li {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.document-list li:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.document-link {
    text-decoration: none;
    color: inherit;
    display: block;
    transition: all 0.3s ease;
}

.document-link:hover {
    transform: translateX(10px);
}

.document-link h4 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #333;
    transition: color 0.3s ease;
}

.document-link:hover h4 {
    color: #d32f2f;
}

.document-link p {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #ffd700;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #ffd700;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #555;
    color: #ccc;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #d32f2f 0%, #f44336 50%, #ff5722 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-logo {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.loading-logo i {
    color: #ffd700;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading-bar {
    width: 300px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin: 2rem auto;
    overflow: hidden;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #ffd700, #ffeb3b, #ffd700);
    background-size: 200% 200%;
    animation: loadingProgress 2s ease-in-out, shimmer 1s ease-in-out infinite;
    border-radius: 2px;
}

@keyframes loadingProgress {
    0% {
        width: 0%;
    }

    100% {
        width: 100%;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Enhanced Sections */
.section {
    position: relative;
    overflow: hidden;
}

.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 49%, rgba(211, 47, 47, 0.02) 50%, transparent 51%);
    pointer-events: none;
}

.section-title {
    position: relative;
}

.section-title::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(211, 47, 47, 0.1) 0%, transparent 70%);
    z-index: -1;
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Enhanced Gallery */
.gallery-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(211, 47, 47, 0.8), rgba(255, 215, 0, 0.8));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.gallery-item:hover::before {
    opacity: 1;
}

.gallery-item:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(211, 47, 47, 0.3);
}

.gallery-overlay {
    z-index: 2;
}

/* Enhanced Video Items */
.video-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(211, 47, 47, 0.1), rgba(255, 215, 0, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.video-item:hover::after {
    opacity: 1;
}

/* Enhanced Document Categories */
.document-category::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 0;
    background: linear-gradient(to bottom, #d32f2f, #ffd700);
    transition: height 0.3s ease;
}

.document-category:hover::before {
    height: 100%;
}

.document-category:hover {
    transform: translateX(10px);
    box-shadow: 0 15px 40px rgba(211, 47, 47, 0.15);
}

/* Enhanced Lightbox Styles */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.lightbox-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.5);
    transform: scale(0.9);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.lightbox-close {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
}

.lightbox-close:hover {
    background: rgba(211, 47, 47, 0.9);
    transform: scale(1.1);
}

.lightbox-image-container {
    display: flex;
    flex-direction: column;
}

.lightbox-image {
    width: 100%;
    max-height: 60vh;
    object-fit: cover;
    display: block;
}

.lightbox-info {
    padding: 2rem;
    background: white;
}

.lightbox-info h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.lightbox-info p {
    color: #666;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.lightbox-actions {
    display: flex;
    gap: 1rem;
}

.lightbox-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.download-btn {
    background: linear-gradient(135deg, #4caf50, #45a049);
    color: white;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.share-btn {
    background: linear-gradient(135deg, #2196f3, #1976d2);
    color: white;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
}

.lightbox-navigation {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(0, 0, 0, 0.7);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.nav-btn {
    width: 35px;
    height: 35px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.nav-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.nav-counter {
    color: white;
    font-size: 0.9rem;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
}

/* Lightbox responsive */
@media (max-width: 768px) {
    .lightbox-content {
        max-width: 95vw;
        max-height: 95vh;
        border-radius: 15px;
    }

    .lightbox-image {
        max-height: 50vh;
    }

    .lightbox-info {
        padding: 1.5rem;
    }

    .lightbox-info h3 {
        font-size: 1.3rem;
    }

    .lightbox-actions {
        flex-direction: column;
    }

    .lightbox-btn {
        justify-content: center;
    }

    .lightbox-navigation {
        bottom: 15px;
        padding: 0.5rem 1rem;
    }

    .nav-btn {
        width: 30px;
        height: 30px;
    }
}

/* Header responsive */
@media (max-width: 768px) {
    .header {
        position: relative;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(5px);
        box-shadow: none;
    }

    .nav-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        padding: 1rem;
        border-radius: 0 0 10px 10px;
        display: none;
        flex-direction: column;
        gap: 0.5rem;
        z-index: 1000;
    }

    .nav-menu.active {
        display: flex;
    }

    .nav-menu li {
        margin: 0.75rem 0;
        opacity: 0;
        transform: translateY(20px);
        animation: slideInUp 0.4s ease forwards;
    }

    .nav-menu.active li:nth-child(1) {
        animation-delay: 0.1s;
    }

    .nav-menu.active li:nth-child(2) {
        animation-delay: 0.15s;
    }

    .nav-menu.active li:nth-child(3) {
        animation-delay: 0.2s;
    }

    .nav-menu.active li:nth-child(4) {
        animation-delay: 0.25s;
    }

    .nav-menu.active li:nth-child(5) {
        animation-delay: 0.3s;
    }

    .nav-menu.active li:nth-child(6) {
        animation-delay: 0.35s;
    }

    .nav-menu.active li:nth-child(7) {
        animation-delay: 0.4s;
    }

    .nav-menu.active li:nth-child(8) {
        animation-delay: 0.45s;
    }

    .nav-menu a {
        display: block;
        padding: 1rem 1.5rem;
        font-size: 1.1rem;
        border-radius: 10px;
        transition: all 0.3s ease;
        margin: 0.5rem 0;
    }

    .nav-menu a:hover {
        background: rgba(211, 47, 47, 0.1);
        color: #d32f2f;
        transform: translateX(10px);
    }

    .hamburger {
        display: flex;
        z-index: 1001;
    }

    .nav-logo {
        font-size: 1.3rem;
    }

    .nav-logo i {
        font-size: 1.1rem;
    }

    .navbar {
        padding: 0.75rem 0;
    }

    .nav-container {
        padding: 0 15px;
    }

    /* Overlay when menu is open */
    .nav-menu.active::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.5);
        z-index: -1;
        backdrop-filter: blur(5px);
    }
}

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Tablet responsive */
@media (max-width: 1024px) and (min-width: 769px) {
    .nav-menu a {
        font-size: 0.95rem;
        padding: 0.75rem 1rem;
    }

    .nav-logo {
        font-size: 1.4rem;
    }

    .nav-container {
        padding: 0 30px;
    }
}

/* ===== RESPONSIVE DESIGN ===== */

/* Extra Large screens (1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1400px;
    }

    .hero-title {
        font-size: 4rem;
    }

    .hero-subtitle {
        font-size: 1.5rem;
    }

    .section-title {
        font-size: 3rem;
    }
}

/* Large screens (992px to 1199px) */
@media (max-width: 1199px) and (min-width: 992px) {
    .container {
        max-width: 1100px;
    }

    .hero {
        padding: 100px 0 80px;
    }

    .hero-title {
        font-size: 3.5rem;
    }

    .gallery-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .documents-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Medium screens - Tablet (768px to 991px) */
@media (max-width: 991px) and (min-width: 768px) {
    .container {
        padding: 0 30px;
    }

    .hero {
        flex-direction: column;
        text-align: center;
        padding: 120px 0 60px;
    }

    .hero-content {
        margin-bottom: 3rem;
    }

    .hero-title {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .content-grid,
    .declaration-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .uprising-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .significance-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .documents-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .timeline-items {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }

    .timeline-item {
        min-width: 140px;
    }

    .section-title {
        font-size: 2.2rem;
    }

    .section {
        padding: 60px 0;
    }
}

/* Small screens - Mobile Large (481px to 767px) */
@media (max-width: 767px) and (min-width: 481px) {
    .container {
        padding: 0 20px;
    }

    .hero {
        padding: 100px 0 50px;
        min-height: 90vh;
    }

    .hero-title {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-date {
        font-size: 1.1rem;
        margin: 1.5rem 0;
    }

    .cta-button {
        padding: 12px 24px;
        font-size: 1rem;
    }

    .section {
        padding: 50px 0;
    }

    .section-title {
        font-size: 2rem;
        margin-bottom: 2rem;
    }

    .content-grid,
    .declaration-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .uprising-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .significance-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .documents-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .timeline-items {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .timeline-item {
        width: 100%;
        max-width: 200px;
        padding: 1rem;
    }

    .prep-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .prep-date {
        position: relative;
        left: auto;
        margin-bottom: 1rem;
    }

    .revolution-timeline {
        gap: 1rem;
    }

    .rev-item {
        padding: 1rem;
    }

    .argument-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* Gallery responsive */
    .gallery-item .image-placeholder {
        height: 200px;
    }

    .gallery-item .image-placeholder h4 {
        font-size: 1rem;
    }

    .gallery-item .image-placeholder p {
        font-size: 0.8rem;
    }

    /* Flag animation responsive */
    .hero-flag {
        transform: scale(0.8);
    }

    .flag-vietnam {
        width: 120px;
        height: 80px;
    }

    .flag-star {
        font-size: 1.5rem;
    }
}

/* Extra Small screens - Mobile (480px and below) */
@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 90px 0 40px;
        min-height: 85vh;
    }

    .hero-title {
        font-size: 2rem;
        line-height: 1.1;
        margin-bottom: 1rem;
    }

    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 1rem;
    }

    .hero-date {
        font-size: 1rem;
        margin: 1rem 0;
    }

    .cta-button {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .section {
        padding: 40px 0;
    }

    .section-title {
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .uprising-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .significance-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .timeline-items {
        flex-direction: column;
        gap: 0.5rem;
    }

    .timeline-item {
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    .prep-item {
        padding: 1rem;
    }

    .prep-date {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }

    .prep-content h3 {
        font-size: 1.1rem;
    }

    .prep-content p {
        font-size: 0.9rem;
    }

    .uprising-card {
        padding: 1.5rem;
    }

    .uprising-card h3 {
        font-size: 1.2rem;
    }

    .uprising-card .date {
        font-size: 0.9rem;
    }

    .uprising-card p {
        font-size: 0.9rem;
    }

    .significance-item {
        padding: 1.5rem;
        text-align: center;
    }

    .significance-item i {
        font-size: 2rem;
    }

    .significance-item h3 {
        font-size: 1.1rem;
    }

    .significance-item p {
        font-size: 0.85rem;
    }

    .revolution-timeline {
        gap: 0.5rem;
    }

    .rev-item {
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    .rev-date {
        font-size: 0.8rem;
    }

    .debate-content {
        gap: 1.5rem;
    }

    .wrong-view,
    .counter-arguments {
        padding: 1.5rem;
    }

    .document-category {
        padding: 1.5rem;
    }

    .document-category h3 {
        font-size: 1.1rem;
    }

    .document-link h4 {
        font-size: 1rem;
    }

    .document-link p {
        font-size: 0.85rem;
    }

    /* Flag animation responsive */
    .hero-flag {
        transform: scale(0.7);
        margin-top: 1rem;
    }

    .flag-vietnam {
        width: 100px;
        height: 65px;
    }

    .flag-star {
        font-size: 1.2rem;
    }

    .hero-flag h3 {
        font-size: 1rem;
        margin-top: 0.5rem;
    }

    .hero-flag p {
        font-size: 0.8rem;
    }

    /* Gallery item responsive */
    .gallery-item .image-placeholder {
        height: 180px;
    }

    .gallery-item::before,
    .gallery-item::after {
        top: 8px;
        right: 8px;
        width: 30px;
        height: 30px;
    }

    .gallery-item::after {
        font-size: 0.9rem;
    }

    /* Footer responsive */
    .footer-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }

    .footer-section {
        margin-bottom: 1rem;
    }

    .footer-section h3,
    .footer-section h4 {
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    .footer-section ul {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }

    .footer-section li {
        margin: 0;
    }

    .footer-bottom {
        text-align: center;
        font-size: 0.85rem;
        padding: 1.5rem 0;
    }
}

/* Landscape orientation for mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .hero {
        min-height: 100vh;
        padding: 80px 0 30px;
    }

    .hero-title {
        font-size: 2.2rem;
    }

    .section {
        padding: 30px 0;
    }

    .timeline-items {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .timeline-item {
        width: auto;
        min-width: 120px;
        padding: 0.5rem;
        font-size: 0.8rem;
    }
}

/* Print styles */
@media print {

    .header,
    .hamburger,
    .cta-button,
    .lightbox,
    .particles {
        display: none !important;
    }

    .hero {
        page-break-after: always;
    }

    .section {
        page-break-inside: avoid;
        padding: 20px 0;
    }

    .section-title {
        color: #000 !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: #fff;
    }
}