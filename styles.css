/* Reset và Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #d32f2f;
}

.nav-logo i {
    margin-right: 10px;
    color: #ffd700;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #d32f2f;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
    color: white;
    padding-top: 80px;
}

.hero-content {
    flex: 1;
    padding: 2rem;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    opacity: 0.9;
    animation: fadeInUp 1s ease 0.2s both;
}

.hero-date {
    font-size: 2rem;
    font-weight: 300;
    margin-bottom: 2rem;
    color: #ffd700;
    animation: fadeInUp 1s ease 0.4s both;
}

.cta-button {
    display: inline-block;
    padding: 15px 30px;
    background: #ffd700;
    color: #333;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    animation: fadeInUp 1s ease 0.6s both;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
}

.hero-image {
    flex: 1;
    text-align: center;
}

/* Video Container Styles */
.video-container {
    position: relative;
    display: inline-block;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 4px;
    animation: fadeInRight 1s ease 0.8s both;
    transition: all 0.3s ease;
}

.video-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

.video-wrapper {
    position: relative;
    width: 100%;
    max-width: 560px;
    height: 315px;
    border-radius: 16px;
    overflow: hidden;
    background: #000;
}

.hero-video {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 16px;
    display: block;
}

.video-info-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(211, 47, 47, 0.85), rgba(255, 193, 7, 0.75));
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    border-radius: 16px;
    pointer-events: none;
}

.video-container:hover .video-info-overlay {
    opacity: 1;
}

.video-badge {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 12px 20px;
    border-radius: 25px;
    margin-bottom: 20px;
    color: #d32f2f;
    font-weight: 600;
    transform: scale(0.9);
    transition: all 0.3s ease;
}

.video-container:hover .video-badge {
    transform: scale(1);
}

.video-badge i {
    font-size: 1.2rem;
    margin-right: 8px;
    color: #d32f2f;
}

.video-title {
    text-align: center;
    color: #fff;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.7);
}

.video-title h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.video-title p {
    font-size: 1rem;
    opacity: 0.9;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: fadeInRight 1s ease 0.8s both;
}

/* Timeline Navigation */
.timeline-nav {
    padding: 4rem 0;
    background: #f8f9fa;
}

.timeline-nav h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
}

.timeline-items {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow-x: auto;
    padding: 2rem 0;
}

.timeline-items::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 3px;
    background: #ddd;
    z-index: 1;
}

.timeline-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.timeline-item:hover {
    transform: scale(1.1);
}

.timeline-item.active .year {
    background: #d32f2f;
    color: white;
}

.year {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: white;
    border: 3px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.event {
    text-align: center;
    font-size: 0.9rem;
    font-weight: 500;
    max-width: 120px;
}

/* Sections */
.section {
    padding: 5rem 0;
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #333;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: #d32f2f;
}

.bg-light {
    background: #f8f9fa;
}

.bg-primary {
    background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
    color: white;
}

.text-white {
    color: white;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.content-text h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: #d32f2f;
}

.content-text p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.8;
}

.content-text ul {
    list-style: none;
    padding-left: 0;
}

.content-text li {
    padding: 0.5rem 0;
    padding-left: 2rem;
    position: relative;
}

.content-text li::before {
    content: '★';
    position: absolute;
    left: 0;
    color: #ffd700;
    font-weight: bold;
}

.content-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* Preparation Timeline */
.preparation-timeline {
    position: relative;
    padding-left: 2rem;
}

.preparation-timeline::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #d32f2f;
}

.prep-item {
    display: flex;
    margin-bottom: 3rem;
    position: relative;
}

.prep-item::before {
    content: '';
    position: absolute;
    left: -2.5rem;
    top: 0.5rem;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: #d32f2f;
}

.prep-date {
    min-width: 120px;
    font-weight: 700;
    color: #d32f2f;
    font-size: 1.1rem;
}

.prep-content {
    flex: 1;
    padding-left: 2rem;
}

.prep-content h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #333;
}

/* Uprising Grid */
.uprising-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.uprising-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.uprising-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.uprising-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #d32f2f;
}

.uprising-card .date {
    font-weight: 700;
    color: #ffd700;
    background: #d32f2f;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    display: inline-block;
    margin-bottom: 1rem;
}

/* Revolution Timeline */
.revolution-timeline {
    display: grid;
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.rev-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.rev-item.highlight {
    background: rgba(255, 215, 0, 0.2);
    border: 2px solid #ffd700;
}

.rev-date {
    min-width: 120px;
    font-weight: 700;
    color: #ffd700;
}

.rev-event {
    flex: 1;
    font-size: 1.1rem;
}

/* Declaration */
.declaration-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

blockquote {
    font-size: 1.5rem;
    font-style: italic;
    padding: 2rem;
    background: #f8f9fa;
    border-left: 5px solid #d32f2f;
    margin-bottom: 2rem;
}

cite {
    display: block;
    text-align: right;
    margin-top: 1rem;
    font-weight: 700;
    color: #d32f2f;
}

/* Significance Grid */
.significance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.significance-item {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.significance-item:hover {
    transform: translateY(-10px);
}

.significance-item i {
    font-size: 3rem;
    color: #d32f2f;
    margin-bottom: 1rem;
}

.significance-item h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

/* Debate Section */
.debate-content {
    max-width: 800px;
    margin: 0 auto;
}

.wrong-view {
    background: #ffebee;
    padding: 2rem;
    border-radius: 10px;
    border-left: 5px solid #f44336;
    margin-bottom: 3rem;
}

.wrong-view h3 {
    color: #d32f2f;
    margin-bottom: 1rem;
}

.counter-arguments h3 {
    color: #2e7d32;
    margin-bottom: 2rem;
}

.argument-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.argument {
    background: #e8f5e8;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 5px solid #4caf50;
}

.argument h4 {
    color: #2e7d32;
    margin-bottom: 0.5rem;
}

/* Gallery */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 2rem;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    transform: translateY(0);
}

/* Video Section */
.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.video-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.video-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.video-thumbnail {
    position: relative;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.video-item:hover .video-thumbnail img {
    transform: scale(1.1);
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(211, 47, 47, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.video-item:hover .play-button {
    background: #d32f2f;
    transform: translate(-50%, -50%) scale(1.1);
}

.video-info {
    padding: 1.5rem;
}

.video-info h4 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.video-info p {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.video-link {
    display: inline-block;
    padding: 0.5rem 1.5rem;
    background: #d32f2f;
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    transition: background 0.3s ease;
}

.video-link:hover {
    background: #b71c1c;
}

/* Documents Section */
.documents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.document-category {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.document-category h3 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #d32f2f;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.document-category h3 i {
    font-size: 1.3rem;
}

.document-list {
    list-style: none;
    padding: 0;
}

.document-list li {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.document-list li:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.document-link {
    text-decoration: none;
    color: inherit;
    display: block;
    transition: all 0.3s ease;
}

.document-link:hover {
    transform: translateX(10px);
}

.document-link h4 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #333;
    transition: color 0.3s ease;
}

.document-link:hover h4 {
    color: #d32f2f;
}

.document-link p {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #ffd700;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #ffd700;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #555;
    color: #ccc;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hamburger {
        display: flex;
    }

    .hero {
        flex-direction: column;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    /* Video responsive */
    .video-container {
        width: 100%;
        max-width: 350px;
        margin: 0 auto;
    }

    .video-wrapper {
        max-width: 350px;
        height: 200px;
    }

    .hero-video {
        width: 100%;
        height: 100%;
    }

    .video-badge {
        padding: 8px 16px;
        font-size: 0.9rem;
    }

    .video-badge i {
        font-size: 1rem;
    }

    .video-title h3 {
        font-size: 1.2rem;
    }

    .video-title p {
        font-size: 0.9rem;
    }

    .content-grid,
    .declaration-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .timeline-items {
        flex-wrap: wrap;
        justify-content: center;
    }

    .timeline-item {
        margin: 1rem;
    }

    .prep-item {
        flex-direction: column;
    }

    .prep-date {
        margin-bottom: 1rem;
    }

    .rev-item {
        flex-direction: column;
        text-align: center;
    }

    .rev-date {
        margin-bottom: 1rem;
    }
}