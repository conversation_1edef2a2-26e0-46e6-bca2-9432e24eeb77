/* Reset và Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Particle Background */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(211, 47, 47, 0.3);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.particle:nth-child(odd) {
    background: rgba(255, 215, 0, 0.3);
    animation-delay: -2s;
}

.particle:nth-child(3n) {
    animation-delay: -4s;
    animation-duration: 8s;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }

    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.8;
    }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    z-index: 1000;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #d32f2f;
    position: relative;
    overflow: hidden;
}

.nav-logo::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.3), transparent);
    transition: left 0.5s ease;
}

.nav-logo:hover::before {
    left: 100%;
}

.nav-logo i {
    margin-right: 10px;
    color: #ffd700;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.nav-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #d32f2f, #f44336);
    transition: left 0.3s ease;
    z-index: -1;
}

.nav-menu a:hover::before {
    left: 0;
}

.nav-menu a:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(211, 47, 47, 0.3);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #d32f2f 0%, #f44336 50%, #ff5722 100%);
    color: white;
    padding-top: 80px;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="star" patternUnits="userSpaceOnUse" width="20" height="20"><polygon points="10,1 13,7 19,7 14.5,11 16.5,17 10,13.5 3.5,17 5.5,11 1,7 7,7" fill="rgba(255,215,0,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23star)"/></svg>') repeat;
    animation: moveStars 20s linear infinite;
}

@keyframes moveStars {
    0% {
        transform: translateX(0) translateY(0);
    }

    100% {
        transform: translateX(-20px) translateY(-20px);
    }
}

.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(255, 215, 0, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.hero-content {
    flex: 1;
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease;
    background: linear-gradient(45deg, #fff, #ffd700, #fff);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: fadeInUp 1s ease, shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {

    0%,
    100% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    opacity: 0.9;
    animation: fadeInUp 1s ease 0.2s both;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-date {
    font-size: 2rem;
    font-weight: 300;
    margin-bottom: 2rem;
    color: #ffd700;
    animation: fadeInUp 1s ease 0.4s both, glow 2s ease-in-out infinite alternate;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

@keyframes glow {
    from {
        text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
    }

    to {
        text-shadow: 0 0 30px rgba(255, 215, 0, 0.8), 0 0 40px rgba(255, 215, 0, 0.6);
    }
}

.cta-button {
    display: inline-block;
    padding: 15px 30px;
    background: linear-gradient(45deg, #ffd700, #ffeb3b, #ffd700);
    background-size: 200% 200%;
    color: #333;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    animation: fadeInUp 1s ease 0.6s both;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 40px rgba(255, 215, 0, 0.4);
    background-position: 100% 100%;
}

.hero-image {
    flex: 1;
    text-align: center;
}

/* Video Container Styles */
.video-container {
    position: relative;
    display: inline-block;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 4px;
    animation: fadeInRight 1s ease 0.8s both;
    transition: all 0.3s ease;
}

.video-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

.video-wrapper {
    position: relative;
    width: 100%;
    max-width: 560px;
    height: 315px;
    border-radius: 16px;
    overflow: hidden;
    background: #000;
}

.hero-video {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 16px;
    display: block;
}

.video-info-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(211, 47, 47, 0.85), rgba(255, 193, 7, 0.75));
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    border-radius: 16px;
    pointer-events: none;
}

.video-container:hover .video-info-overlay {
    opacity: 1;
}

.video-badge {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 12px 20px;
    border-radius: 25px;
    margin-bottom: 20px;
    color: #d32f2f;
    font-weight: 600;
    transform: scale(0.9);
    transition: all 0.3s ease;
}

.video-container:hover .video-badge {
    transform: scale(1);
}

.video-badge i {
    font-size: 1.2rem;
    margin-right: 8px;
    color: #d32f2f;
}

.video-title {
    text-align: center;
    color: #fff;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.7);
}

.video-title h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.video-title p {
    font-size: 1rem;
    opacity: 0.9;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: fadeInRight 1s ease 0.8s both;
}

/* Timeline Navigation */
.timeline-nav {
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    overflow: hidden;
}

.timeline-nav::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><circle cx="30" cy="30" r="2" fill="rgba(211,47,47,0.1)"/></svg>') repeat;
    animation: movePattern 30s linear infinite;
}

@keyframes movePattern {
    0% {
        transform: translateX(0) translateY(0);
    }

    100% {
        transform: translateX(60px) translateY(60px);
    }
}

.timeline-nav h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
    position: relative;
    z-index: 2;
    background: linear-gradient(45deg, #d32f2f, #f44336, #d32f2f);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: shimmer 4s ease-in-out infinite;
}

.timeline-items {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow-x: auto;
    padding: 2rem 0;
}

.timeline-items::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #d32f2f, #ffd700, #d32f2f);
    background-size: 200% 200%;
    animation: shimmer 3s ease-in-out infinite;
    z-index: 1;
    border-radius: 2px;
    box-shadow: 0 2px 10px rgba(211, 47, 47, 0.3);
}

.timeline-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    cursor: pointer;
    transition: all 0.3s ease;
}

.timeline-item:hover {
    transform: scale(1.15) translateY(-5px);
}

.timeline-item.active .year {
    background: linear-gradient(135deg, #d32f2f, #f44336);
    color: white;
    box-shadow: 0 10px 30px rgba(211, 47, 47, 0.4);
    animation: pulse-active 2s ease-in-out infinite;
}

@keyframes pulse-active {

    0%,
    100% {
        transform: scale(1);
        box-shadow: 0 10px 30px rgba(211, 47, 47, 0.4);
    }

    50% {
        transform: scale(1.05);
        box-shadow: 0 15px 40px rgba(211, 47, 47, 0.6);
    }
}

.year {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: white;
    border: 3px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.event {
    text-align: center;
    font-size: 0.9rem;
    font-weight: 500;
    max-width: 120px;
}

/* Sections */
.section {
    padding: 5rem 0;
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #333;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: #d32f2f;
}

.bg-light {
    background: #f8f9fa;
}

.bg-primary {
    background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
    color: white;
}

.text-white {
    color: white;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.content-text h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: #d32f2f;
}

.content-text p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.8;
}

.content-text ul {
    list-style: none;
    padding-left: 0;
}

.content-text li {
    padding: 0.5rem 0;
    padding-left: 2rem;
    position: relative;
}

.content-text li::before {
    content: '★';
    position: absolute;
    left: 0;
    color: #ffd700;
    font-weight: bold;
}

.content-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* Preparation Timeline */
.preparation-timeline {
    position: relative;
    padding-left: 2rem;
}

.preparation-timeline::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #d32f2f;
}

.prep-item {
    display: flex;
    margin-bottom: 3rem;
    position: relative;
}

.prep-item::before {
    content: '';
    position: absolute;
    left: -2.5rem;
    top: 0.5rem;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: #d32f2f;
}

.prep-date {
    min-width: 120px;
    font-weight: 700;
    color: #d32f2f;
    font-size: 1.1rem;
}

.prep-content {
    flex: 1;
    padding-left: 2rem;
}

.prep-content h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #333;
}

/* Uprising Grid */
.uprising-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.uprising-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(211, 47, 47, 0.1);
}

.uprising-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(211, 47, 47, 0.1), transparent);
    transition: left 0.6s ease;
}

.uprising-card:hover::before {
    left: 100%;
}

.uprising-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 25px 50px rgba(211, 47, 47, 0.2);
    border-color: rgba(211, 47, 47, 0.3);
}

.uprising-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #d32f2f;
}

.uprising-card .date {
    font-weight: 700;
    color: #ffd700;
    background: linear-gradient(135deg, #d32f2f, #f44336);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    display: inline-block;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(211, 47, 47, 0.3);
    animation: float-date 3s ease-in-out infinite;
    position: relative;
    overflow: hidden;
}

.uprising-card .date::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.uprising-card:hover .date::before {
    left: 100%;
}

@keyframes float-date {

    0%,
    100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-3px);
    }
}

/* Revolution Timeline */
.revolution-timeline {
    display: grid;
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.rev-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.rev-item.highlight {
    background: rgba(255, 215, 0, 0.2);
    border: 2px solid #ffd700;
}

.rev-date {
    min-width: 120px;
    font-weight: 700;
    color: #ffd700;
}

.rev-event {
    flex: 1;
    font-size: 1.1rem;
}

/* Declaration */
.declaration-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

blockquote {
    font-size: 1.5rem;
    font-style: italic;
    padding: 2rem;
    background: #f8f9fa;
    border-left: 5px solid #d32f2f;
    margin-bottom: 2rem;
}

cite {
    display: block;
    text-align: right;
    margin-top: 1rem;
    font-weight: 700;
    color: #d32f2f;
}

/* Significance Grid */
.significance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.significance-item {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.significance-item:hover {
    transform: translateY(-10px);
}

.significance-item i {
    font-size: 3rem;
    color: #d32f2f;
    margin-bottom: 1rem;
}

.significance-item h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

/* Debate Section */
.debate-content {
    max-width: 800px;
    margin: 0 auto;
}

.wrong-view {
    background: #ffebee;
    padding: 2rem;
    border-radius: 10px;
    border-left: 5px solid #f44336;
    margin-bottom: 3rem;
}

.wrong-view h3 {
    color: #d32f2f;
    margin-bottom: 1rem;
}

.counter-arguments h3 {
    color: #2e7d32;
    margin-bottom: 2rem;
}

.argument-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.argument {
    background: #e8f5e8;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 5px solid #4caf50;
}

.argument h4 {
    color: #2e7d32;
    margin-bottom: 0.5rem;
}

/* Gallery */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 2rem;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    transform: translateY(0);
}

/* Video Section */
.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.video-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.video-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.video-thumbnail {
    position: relative;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.video-item:hover .video-thumbnail img {
    transform: scale(1.1);
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(211, 47, 47, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.video-item:hover .play-button {
    background: #d32f2f;
    transform: translate(-50%, -50%) scale(1.1);
}

.video-info {
    padding: 1.5rem;
}

.video-info h4 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.video-info p {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.video-link {
    display: inline-block;
    padding: 0.5rem 1.5rem;
    background: #d32f2f;
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    transition: background 0.3s ease;
}

.video-link:hover {
    background: #b71c1c;
}

/* Documents Section */
.documents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.document-category {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.document-category h3 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #d32f2f;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.document-category h3 i {
    font-size: 1.3rem;
}

.document-list {
    list-style: none;
    padding: 0;
}

.document-list li {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.document-list li:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.document-link {
    text-decoration: none;
    color: inherit;
    display: block;
    transition: all 0.3s ease;
}

.document-link:hover {
    transform: translateX(10px);
}

.document-link h4 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #333;
    transition: color 0.3s ease;
}

.document-link:hover h4 {
    color: #d32f2f;
}

.document-link p {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #ffd700;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #ffd700;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #555;
    color: #ccc;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #d32f2f 0%, #f44336 50%, #ff5722 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-logo {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.loading-logo i {
    color: #ffd700;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading-bar {
    width: 300px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin: 2rem auto;
    overflow: hidden;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #ffd700, #ffeb3b, #ffd700);
    background-size: 200% 200%;
    animation: loadingProgress 2s ease-in-out, shimmer 1s ease-in-out infinite;
    border-radius: 2px;
}

@keyframes loadingProgress {
    0% {
        width: 0%;
    }

    100% {
        width: 100%;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hamburger {
        display: flex;
    }

    .hero {
        flex-direction: column;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    /* Video responsive */
    .video-container {
        width: 100%;
        max-width: 350px;
        margin: 0 auto;
    }

    .video-wrapper {
        max-width: 350px;
        height: 200px;
    }

    .hero-video {
        width: 100%;
        height: 100%;
    }

    .video-badge {
        padding: 8px 16px;
        font-size: 0.9rem;
    }

    .video-badge i {
        font-size: 1rem;
    }

    .video-title h3 {
        font-size: 1.2rem;
    }

    .video-title p {
        font-size: 0.9rem;
    }

    .content-grid,
    .declaration-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .timeline-items {
        flex-wrap: wrap;
        justify-content: center;
    }

    .timeline-item {
        margin: 1rem;
    }

    .prep-item {
        flex-direction: column;
    }

    .prep-date {
        margin-bottom: 1rem;
    }

    .rev-item {
        flex-direction: column;
        text-align: center;
    }

    .rev-date {
        margin-bottom: 1rem;
    }

    .loading-bar {
        width: 250px;
    }

    .loading-logo {
        font-size: 1.5rem;
    }
}

/* Enhanced Sections */
.section {
    position: relative;
    overflow: hidden;
}

.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 49%, rgba(211, 47, 47, 0.02) 50%, transparent 51%);
    pointer-events: none;
}

.section-title {
    position: relative;
}

.section-title::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(211, 47, 47, 0.1) 0%, transparent 70%);
    z-index: -1;
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Enhanced Gallery */
.gallery-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(211, 47, 47, 0.8), rgba(255, 215, 0, 0.8));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.gallery-item:hover::before {
    opacity: 1;
}

.gallery-item:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(211, 47, 47, 0.3);
}

.gallery-overlay {
    z-index: 2;
}

/* Enhanced Video Items */
.video-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(211, 47, 47, 0.1), rgba(255, 215, 0, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.video-item:hover::after {
    opacity: 1;
}

/* Enhanced Document Categories */
.document-category::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 0;
    background: linear-gradient(to bottom, #d32f2f, #ffd700);
    transition: height 0.3s ease;
}

.document-category:hover::before {
    height: 100%;
}

.document-category:hover {
    transform: translateX(10px);
    box-shadow: 0 15px 40px rgba(211, 47, 47, 0.15);
}